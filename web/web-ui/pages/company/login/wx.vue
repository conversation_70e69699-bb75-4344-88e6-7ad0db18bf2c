<template>
    <div class="login pt-[30px]">
        <div class="mx-[auto] w-[450px]">
            <el-tabs class="demo-tabs">
                <el-form label-width="auto" style="max-width: 450px"
                    class="mt-[1rem] rounded-xl bg-white p-[1rem] h-[390px]">
                    <h1 class="ml-[20px] mt-[15px] text-left text-[20px] text-[#333]">
                        微信登录
                    </h1>
                    <div class="m-[2rem] mb-[15px] mt-[15px] h-[198px]">
                        <p class='text-sm text-[#888]'>请使用微信扫描二维码登录</p>
                        <div
                            class="mx-auto h-[170px] w-[170px] flex items-center justify-center border border-[#f1f1f1] mt-[20px]">
                            <img :src="qrcodeUrl" class="h-[150px] w-[150px]">
                        </div>
                    </div>
                    <h2 class="relative mt-[40px] border-t border-[#E7E7E7] text-[14px] text-[#318AD9] ">
                        <span
                            class="absolute top-[-20px] inline-block translate-x-(-1/2) bg-white p-[10px]">其他登录方式</span>
                    </h2>
                    <div class="mx-auto mt-[20px] w-[25%] flex justify-center justify-between">
                        <img src="/images/kepu-dn.png" @click="$router.push({ name: 'COMPANY_LOGIN' })">
                        <img src="/images/dl-ca.png" @click="loginCa">
                    </div>
                </el-form>
            </el-tabs>
        </div>
        <el-dialog v-model="pwdShow" title="请输入登录密码" width="400px" center align-center :close-on-click-modal="false"
            destroy-on-close @close="handleLoginCancel">
            <el-form ref="loginRef" :model="loginForm">
                <el-form-item prop="password" :rules="[{ required: true, message: '请输入密码', trigger: 'blur' }]">
                    <el-input v-model="loginForm.password" type="password" show-password placeholder="请输入密码"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="pwdShow = false">取消</el-button>
                <el-button type="primary" @click="handleLogin">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { getAuthorizationStatus, getLoginId, loginByWx } from '~/api/login/wx';
import { ECOLUMN_CODE } from '~/store/column';
import QRCode from 'qrcode'
import type { FormInstance } from 'element-plus';

definePageMeta({
    name: ECOLUMN_CODE.COMPANY_LOGIN_WX,
    layout: 'company-login'
})

const config = useRuntimeConfig()
const pwdShow = ref(false)
const loginForm = reactive({
    uuid: '',
    password: ''
})
const qrcodeUrl = ref('')
let qrIntervalId: NodeJS.Timeout | null = null
let statusIntervalId: NodeJS.Timeout | null = null
const generateQRCode = async () => {
    try {
        const { data: uuid } = await getLoginId() // 获取 uuid
        loginForm.uuid = uuid ?? ''
        if (uuid) {
            const urlWithUuid = `${config.public.wxAuthUrl}/company/wx/login/${loginForm.uuid}` // 将 uuid 拼接到 URL 中
            qrcodeUrl.value = await QRCode.toDataURL(urlWithUuid, {
                margin: 1,
            }) // 生成二维码

            // 开始轮询获取授权状态
            if (statusIntervalId != null) {
                clearInterval(statusIntervalId)
            }
            statusIntervalId = setInterval(async () => {
                try {
                    const res = await getAuthorizationStatus(uuid)
                    if (res.data == 1) {
                        pwdShow.value = true
                        // ElMessage.success('微信绑定成功')
                    }
                } catch (error) {
                    console.error('Error checking authorization status:', error)
                }
            }, 2000)
        } else {
            ElMessage.error('获取微信二维码失败')
        }
    } catch (error) {
        console.error('Error generating QR code:', error)
    }
}
onMounted(() => {
    closeInterval()
    generateQRCode() // 初始生成一次二维码

    // 每两分钟重新生成一次二维码
    qrIntervalId = setInterval(generateQRCode, 600000)
})

const loginRef = ref<FormInstance>()
const handleLogin = () => {
    loginRef.value?.validate(async (valid) => {
        if (!valid) return
        if (valid) {
            const res = await loginByWx(loginForm)
            if (res.code == 200) {
                ElMessage.success('登录成功')
                useCookie(TOKEN_KEY, { path: config.public.baseUrl }).value = 'Bearer ' + res.token
                const hasShown = localStorage.getItem('welcomeDialogClosed');
                if (hasShown) {
                        // 初次访问任意路由时设置标志位
                        localStorage.setItem('welcomeDialogClosed', 'null');
                }
                // 登录成功后跳转到首页
                useRouter().push({ name: ECOLUMN_CODE.COMPANY_SERVICE_TRAIN })

            } else {
                ElMessage.error(res.msg)
            }
        }
    })
}

const closeInterval = () => {
    if (qrIntervalId != null) {
        clearInterval(qrIntervalId)
    }
    if (statusIntervalId != null) {
        clearInterval(statusIntervalId)
    }
}

const handleLoginCancel = () => {
    closeInterval()
    generateQRCode() // 初始生成一次二维码

    // 每两分钟重新生成一次二维码
    qrIntervalId = setInterval(generateQRCode, 600000)
}

const loginCa = () => {
  const link = document.createElement('a');
  link.href = config.public.caUrl;
  link.target = '_blank';
  link.rel = 'noopener noreferrer';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link); // 清理DOM
}

onUnmounted(() => {
    closeInterval()
})

</script>
<style scoped>
:deep(.el-input__wrapper) {
    box-shadow: none;
    border-bottom: solid 1px #dcdcdc;
    border-radius: 0;
}

:deep(.el-tabs__nav) {
    justify-content: center !important;
    float: none;
}

:deep(.el-tabs__active-bar) {
    background-color: #fff;
}

:deep(.el-tabs__item.is-active, .el-tabs__item:hover) {
    color: #fff;
    width: 75px;
}

:deep(.el-tabs__item) {
    color: #fff;
}

:deep(.el-tabs__item) {
    padding: 0 50px;
}

:deep(.el-tabs__nav-wrap::after) {
    height: 0;
}
</style>